.PHONY: install lint format help ocr-kin

help:  ## Show this help message
	@echo "Available targets:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}'

install:  ## Install dependencies and pre-commit hooks
	poetry install
	poetry run pre-commit install

lint:  ## Run linting checks
	poetry run ruff check .

format:  ## Format code with black
	poetry run black .

check:  ## Run all checks (lint + format check)
	poetry run black --check .
	poetry run ruff check .

ocr-kin:  ## Build and run Kinyarwanda OCR pipeline
	docker build -f scripts/ocr_kin/Dockerfile -t ocr-kin .
	docker run --rm -v $$PWD:/app ocr-kin --pdf raw/kin1909/kin1909.pdf \
		--out data/verses_kin_1909.jsonl
