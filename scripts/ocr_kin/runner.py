#!/usr/bin/env python3
"""Kinyarwanda OCR Pipeline Runner.

This script serves as the main entry point for the Kinyarwanda OCR pipeline.
It orchestrates the entire process from PDF input to structured JSONL output.
"""

import argparse
import sys
from pathlib import Path


def main():
    """Main entry point for the OCR pipeline."""
    parser = argparse.ArgumentParser(
        description="Kinyarwanda OCR Pipeline",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
    )

    parser.add_argument(
        "--pdf", type=Path, required=True, help="Path to input PDF file"
    )

    parser.add_argument(
        "--out", type=Path, required=True, help="Path to output JSONL file"
    )

    parser.add_argument(
        "--tmp",
        type=Path,
        default=Path("./tmp"),
        help="Temporary directory for intermediate files",
    )

    args = parser.parse_args()

    # For now, just print scaffold confirmation and exit
    print("Pipeline scaffold ok")
    print(f"Input PDF: {args.pdf}")
    print(f"Output JSONL: {args.out}")
    print(f"Temp directory: {args.tmp}")

    # TODO: Implement the full pipeline:
    # 1. Split PDF to pages using splitter.py
    # 2. OCR each page using ocr_page.py
    # 3. Assemble and clean text using assemble.py
    # 4. Parse to USFM structure using structural_pass.py
    # 5. Validate results using validator.py
    # 6. Output to JSONL format

    return 0


if __name__ == "__main__":
    sys.exit(main())
