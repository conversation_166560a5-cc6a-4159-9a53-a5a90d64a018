# Kinyarwanda OCR Pipeline Dockerfile
FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    tesseract-ocr \
    poppler-utils \
    && rm -rf /var/lib/apt/lists/*

# Install Python package management tools
RUN pip install --no-cache-dir pip-tools poetry

# Set working directory
WORKDIR /app

# Copy poetry configuration files
COPY pyproject.toml poetry.lock* ./

# Install Python dependencies
RUN poetry config virtualenvs.create false \
    && poetry install --no-dev

# Copy the entire project
COPY . .

# Set the entrypoint to run the OCR pipeline
ENTRYPOINT ["python", "-m", "scripts.ocr_kin.runner"]
