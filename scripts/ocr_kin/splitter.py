"""PDF to PNG splitter module.

This module handles splitting PDF files into individual PNG pages
for OCR processing.
"""

from pathlib import Path
from typing import List


def split_pdf_to_pages(pdf_path: Path, output_dir: Path) -> List[Path]:
    """Split a PDF file into individual PNG pages.

    Args:
        pdf_path: Path to the input PDF file
        output_dir: Directory to save the PNG pages

    Returns:
        List of paths to the generated PNG files

    TODO: Implement PDF splitting using poppler-utils
    """
    # Stub implementation
    print(f"TODO: Split {pdf_path} into PNG pages in {output_dir}")
    return []
